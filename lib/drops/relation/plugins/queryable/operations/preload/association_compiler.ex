defmodule Drops.Relation.Plugins.Queryable.Operations.Preload.AssociationCompiler do
  use Drops.Relation.Plugins.Queryable.Operations.Compiler

  @spec visit(map(), map()) :: {:ok, Ecto.Query.t()} | {:error, [String.t()]}
  def visit(relation, %{query: query, opts: opts}) when is_list(opts) do
    # Extract preload-specific options
    preload_opts = Keyword.get(opts, :preload, [])
    preload_value = Keyword.get(preload_opts, :preload)

    if preload_value do
      case validate_preload_associations(preload_value, relation) do
        [] ->
          # No validation errors
          valid_preloads = filter_valid_associations(preload_value, relation)
          result_query = apply_preloads(query, valid_preloads)
          {:ok, result_query}

        errors ->
          {:error, errors}
      end
    else
      {:ok, query}
    end
  end

  defp filter_valid_associations(preload_value, relation) do
    available_associations = get_available_associations(relation)

    case preload_value do
      association when is_atom(association) ->
        if association in available_associations, do: association, else: nil

      associations when is_list(associations) ->
        filter_valid_associations_from_list(associations, available_associations)

      _ ->
        nil
    end
  end

  defp get_available_associations(relation) do
    relation_module = relation.__struct__

    if function_exported?(relation_module, :__schema_module__, 0) do
      schema_module = relation_module.__schema_module__()

      if function_exported?(schema_module, :__schema__, 1) do
        schema_module.__schema__(:associations)
      else
        []
      end
    else
      []
    end
  end

  # Validation functions
  defp validate_preload_associations(preload_value, relation) do
    available_associations = get_available_associations(relation)

    case preload_value do
      association when is_atom(association) ->
        if association in available_associations do
          []
        else
          [build_error(:custom, "association :#{association} is not defined")]
        end

      associations when is_list(associations) ->
        Enum.flat_map(associations, fn
          association when is_atom(association) ->
            if association in available_associations do
              []
            else
              [build_error(:custom, "association :#{association} is not defined")]
            end

          {association, _nested} when is_atom(association) ->
            # For nested associations like [user: :profile], validate the root association
            # Note: We don't validate nested associations here as they depend on the target schema
            if association in available_associations do
              []
            else
              [build_error(:custom, "association :#{association} is not defined")]
            end

          invalid ->
            [build_error(:custom, "invalid preload specification: #{inspect(invalid)}")]
        end)

      invalid ->
        [build_error(:custom, "invalid preload value: #{inspect(invalid)}")]
    end
  end

  # Private functions

  defp filter_valid_associations_from_list(associations, available_associations) do
    Enum.filter(associations, fn
      association when is_atom(association) ->
        association in available_associations

      {association, _nested} when is_atom(association) ->
        # For nested associations like [user: :profile], validate the root association
        # Note: We don't validate nested associations here as they depend on the target schema
        association in available_associations

      _ ->
        false
    end)
  end

  defp apply_preloads(queryable, nil), do: queryable
  defp apply_preloads(queryable, []), do: queryable

  defp apply_preloads(queryable, preloads) when is_atom(preloads) do
    from(q in queryable, preload: ^preloads)
  end

  defp apply_preloads(queryable, preloads) when is_list(preloads) do
    from(q in queryable, preload: ^preloads)
  end
end
