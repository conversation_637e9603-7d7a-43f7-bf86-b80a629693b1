defmodule Drops.Relation.Plugins.Queryable.Operations.Compiler do
  @moduledoc """
  Abstract base compiler for Queryable operations.

  This module provides common visitor patterns, error handling helpers, and base functionality
  to reduce code duplication across operation compilers like Restrict, Order, and Preload.

  ## Usage

      defmodule MyOperationCompiler do
        use Drops.Relation.Plugins.Queryable.Operations.Compiler

        def visit(relation, %{query: query, opts: opts}) do
          # Implementation specific to your operation
          with {:ok, validated_opts} <- validate_opts(opts, relation),
               {:ok, result_query} <- apply_operation(query, validated_opts) do
            {:ok, result_query}
          else
            {:error, errors} -> {:error, errors}
          end
        end

        # Implement operation-specific validation and application
        defp validate_opts(opts, relation), do: # ...
        defp apply_operation(query, opts), do: # ...
      end

  ## Provided Helpers

  - `build_error/2` - Creates standardized error messages
  - `build_errors/1` - Combines multiple errors into a list
  - `extract_field_names/1` - Extracts field names from schema
  - `find_field/2` - Finds a field in schema by name
  - `validate_field_exists/2` - Validates that a field exists in schema
  """

  alias Drops.Relation.Schema

  @doc """
  Main entry point for operation compilers.

  This function should be implemented by each operation compiler to handle
  the specific logic for that operation.

  ## Parameters

  - `relation` - The relation struct containing schema and other metadata
  - `context` - A map containing `:query` and `:opts` keys

  ## Returns

  - `{:ok, Ecto.Query.t()}` - Successfully compiled query
  - `{:error, [String.t()]}` - List of validation errors
  """
  @callback visit(map(), map()) :: {:ok, Ecto.Query.t()} | {:error, [String.t()]}

  defmacro __using__(_opts) do
    quote do
      @behaviour Drops.Relation.Plugins.Queryable.Operations.Compiler

      import Ecto.Query
      import Drops.Relation.Plugins.Queryable.Operations.Compiler

      @doc """
      Main entry point for the operation compiler.
      Must be implemented by the using module.
      """
      def visit(relation, context)

      defoverridable visit: 2
    end
  end

  @doc """
  Builds a standardized error message.

  ## Examples

      iex> build_error(:field_not_found, "email")
      "Field 'email' not found in schema"

      iex> build_error(:invalid_value, %{field: "age", value: "invalid"})
      "Invalid value 'invalid' for field 'age'"
  """
  def build_error(:field_not_found, field_name) when is_atom(field_name) do
    "Field '#{field_name}' not found in schema"
  end

  def build_error(:invalid_value, %{field: field_name, value: value}) do
    "Invalid value '#{inspect(value)}' for field '#{field_name}'"
  end

  def build_error(:not_nullable, field_name) when is_atom(field_name) do
    "#{field_name} is not nullable, comparing to `nil` is not allowed"
  end

  def build_error(:not_boolean_field, %{field: field_name, value: value}) do
    "#{field_name} is not a boolean field, comparing to boolean value `#{value}` is not allowed"
  end

  def build_error(:custom, message) when is_binary(message) do
    message
  end

  @doc """
  Combines multiple errors into a flat list.

  ## Examples

      iex> build_errors([["error1", "error2"], [], ["error3"]])
      ["error1", "error2", "error3"]
  """
  def build_errors(error_lists) when is_list(error_lists) do
    Enum.flat_map(error_lists, fn
      errors when is_list(errors) -> errors
      error when is_binary(error) -> [error]
      _ -> []
    end)
  end

  @doc """
  Extracts field names from a schema.

  ## Examples

      iex> schema = %Schema{fields: [%Schema.Field{name: :id}, %Schema.Field{name: :name}]}
      iex> extract_field_names(schema)
      [:id, :name]
  """
  def extract_field_names(%Schema{fields: fields}) do
    Enum.map(fields, & &1.name)
  end

  @doc """
  Finds a field in schema by name.

  ## Examples

      iex> schema = %Schema{fields: [%Schema.Field{name: :id}, %Schema.Field{name: :name}]}
      iex> find_field(schema, :name)
      %Schema.Field{name: :name}

      iex> find_field(schema, :nonexistent)
      nil
  """
  def find_field(%Schema{} = schema, field_name) when is_atom(field_name) do
    Schema.find_field(schema, field_name)
  end

  @doc """
  Validates that a field exists in the schema.

  ## Examples

      iex> schema = %Schema{fields: [%Schema.Field{name: :name}]}
      iex> validate_field_exists(schema, :name)
      {:ok, %Schema.Field{name: :name}}

      iex> validate_field_exists(schema, :nonexistent)
      {:error, "Field 'nonexistent' not found in schema"}
  """
  def validate_field_exists(%Schema{} = schema, field_name) when is_atom(field_name) do
    case find_field(schema, field_name) do
      nil -> {:error, build_error(:field_not_found, field_name)}
      field -> {:ok, field}
    end
  end

  @doc """
  Filters options to only include valid field names.

  ## Examples

      iex> schema = %Schema{fields: [%Schema.Field{name: :name}, %Schema.Field{name: :email}]}
      iex> filter_valid_fields([name: "John", email: "<EMAIL>", invalid: "value"], schema)
      [name: "John", email: "<EMAIL>"]
  """
  def filter_valid_fields(opts, %Schema{} = schema) when is_list(opts) do
    valid_field_names = extract_field_names(schema)

    Enum.filter(opts, fn {field_name, _value} ->
      field_name in valid_field_names
    end)
  end

  @doc """
  Validates that all provided field names exist in the schema.

  ## Examples

      iex> schema = %Schema{fields: [%Schema.Field{name: :name}]}
      iex> validate_all_fields_exist([name: "John"], schema)
      {:ok, [name: "John"]}

      iex> validate_all_fields_exist([name: "John", invalid: "value"], schema)
      {:error, ["Field 'invalid' not found in schema"]}
  """
  def validate_all_fields_exist(opts, %Schema{} = schema) when is_list(opts) do
    errors =
      Enum.flat_map(opts, fn {field_name, _value} ->
        case validate_field_exists(schema, field_name) do
          {:ok, _field} -> []
          {:error, error} -> [error]
        end
      end)

    case errors do
      [] -> {:ok, opts}
      errors -> {:error, errors}
    end
  end

  @doc """
  Checks if a field is semantically boolean based on its type and metadata.

  For SQLite, boolean fields are stored as integers but are considered boolean
  if they have boolean default values (true/false).

  ## Examples

      iex> field = %Schema.Field{type: :boolean}
      iex> is_boolean_field?(field)
      true

      iex> field = %Schema.Field{type: :integer, meta: %{default: true}}
      iex> is_boolean_field?(field)
      true

      iex> field = %Schema.Field{type: :integer, meta: %{default: 1}}
      iex> is_boolean_field?(field)
      false
  """
  def is_boolean_field?(%Schema.Field{type: :boolean}), do: true

  def is_boolean_field?(%Schema.Field{type: :integer, meta: meta}) do
    # Check if this is a boolean field stored as integer (common in SQLite)
    # Only consider it boolean if the default value is explicitly true/false
    case Map.get(meta, :default) do
      default when default in [true, false] -> true
      _ -> false
    end
  end

  def is_boolean_field?(_), do: false

  @doc """
  Validates that a field value is compatible with the field type.

  ## Examples

      iex> field = %Schema.Field{name: :active, type: :boolean}
      iex> validate_field_value(field, true)
      []

      iex> field = %Schema.Field{name: :name, type: :string, meta: %{nullable: false}}
      iex> validate_field_value(field, nil)
      ["name is not nullable, comparing to `nil` is not allowed"]
  """
  def validate_field_value(%Schema.Field{name: field_name, meta: meta}, nil) do
    if Map.get(meta, :nullable, true) do
      []
    else
      [build_error(:not_nullable, field_name)]
    end
  end

  def validate_field_value(%Schema.Field{name: field_name} = field, value) when is_boolean(value) do
    case is_boolean_field?(field) do
      true ->
        []

      false ->
        [build_error(:not_boolean_field, %{field: field_name, value: value})]
    end
  end

  def validate_field_value(%Schema.Field{}, value) when is_list(value) do
    # List values are always valid for IN expressions
    []
  end

  def validate_field_value(%Schema.Field{}, _value) do
    # Other values are valid by default
    []
  end
end
