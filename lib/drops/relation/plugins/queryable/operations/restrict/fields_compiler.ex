defmodule Drops.Relation.Plugins.Queryable.Operations.Restrict.FieldsCompiler do
  use Drops.Relation.Plugins.Queryable.Operations.Compiler

  @spec visit(map(), map()) :: {:ok, Ecto.Query.t()} | {:error, [String.t()]}
  def visit(relation, %{query: query, opts: opts}) when is_list(opts) do
    # Extract restrict-specific options
    restrict_opts = Keyword.get(opts, :restrict, [])

    # Filter to only valid field options
    valid_field_opts = filter_valid_fields(restrict_opts, relation.schema)

    # Collect all validation errors first
    errors =
      Enum.flat_map(valid_field_opts, fn {field_name, value} ->
        field_struct = find_field(relation.schema, field_name)
        validate_field_value(field_struct, value)
      end)

    case errors do
      [] ->
        # No errors, proceed with building the query
        result_query =
          Enum.reduce(valid_field_opts, query, fn {field_name, value}, acc_query ->
            field_struct = find_field(relation.schema, field_name)
            apply_field_condition({field_struct, value}, %{query: acc_query})
          end)

        {:ok, result_query}

      errors ->
        {:error, errors}
    end
  end

  # Query building functions
  defp apply_field_condition({field, value}, %{query: query}) when is_list(value) do
    where(query, [r], field(r, ^field.name) in ^value)
  end

  defp apply_field_condition({field, nil}, %{query: query}) do
    # We already validated that nullable fields can be nil, so this is safe
    where(query, [r], is_nil(field(r, ^field.name)))
  end

  defp apply_field_condition({field, value}, %{query: query}) when is_boolean(value) do
    # Use IS TRUE/IS FALSE for proper boolean fields
    if is_boolean_field?(field) do
      if value do
        where(query, [r], field(r, ^field.name) == true)
      else
        where(query, [r], field(r, ^field.name) == false)
      end
    else
      # This should not happen due to validation, but keep as fallback
      where(query, [r], field(r, ^field.name) == ^value)
    end
  end

  defp apply_field_condition({field, value}, %{query: query}) do
    where(query, [r], field(r, ^field.name) == ^value)
  end
end
