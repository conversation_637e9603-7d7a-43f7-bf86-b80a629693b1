defmodule Drops.Relation.Plugins.Queryable do
  alias Drops.Relation.Generator
  alias Drops.Relation.Plugins.Queryable.Operations
  alias Drops.Relation.Plugins.Queryable.InvalidQueryError

  use Drops.Relation.Plugin do
    defstruct([:repo, :schema, :queryable, operations: [], opts: []])
  end

  def on(:before_compile, relation, %{opts: opts}) do
    ecto_schema_module = ecto_schema_module(relation)

    ecto_funcs =
      quote do
        defdelegate __schema__(key), to: unquote(ecto_schema_module)
        defdelegate __schema__(key, value), to: unquote(ecto_schema_module)

        def __schema_module__, do: unquote(ecto_schema_module)
      end

    quote do
      unquote(ecto_funcs)

      @spec repo() :: module()
      def repo, do: unquote(opts[:repo])

      def new(), do: new([])

      def new(opts) do
        new(__schema_module__(), opts)
      end

      def new(queryable, opts) do
        Kernel.struct(__MODULE__, %{
          queryable: queryable,
          schema: schema(),
          repo: repo(),
          operations: [],
          opts: opts
        })
      end

      @doc """
      Adds an operation to the relation if it's not already present.

      This function provides smart operation deduplication by only adding
      operations that aren't already in the operations list.

      ## Examples

          relation = MyRelation.new()
          relation = add_operation(relation, :restrict)
          relation = add_operation(relation, :restrict)  # Won't duplicate
          assert relation.operations == [:restrict]
      """
      def add_operation(%__MODULE__{operations: operations} = relation, operation) do
        if operation in operations do
          relation
        else
          %{relation | operations: operations ++ [operation]}
        end
      end

      @doc """
      Merges options into the relation's opts, scoped by operation.

      This function merges new options into the relation's existing options,
      organizing them by operation type for better structure.

      ## Examples

          relation = MyRelation.new()
          relation = merge_operation_opts(relation, :restrict, name: "John")
          relation = merge_operation_opts(relation, :order, order: :name)
          # relation.opts == [restrict: [name: "John"], order: [order: :name]]
      """
      def merge_operation_opts(%__MODULE__{opts: current_opts} = relation, operation, new_opts) do
        current_operation_opts = Keyword.get(current_opts, operation, [])
        merged_operation_opts = Keyword.merge(current_operation_opts, new_opts)
        updated_opts = Keyword.put(current_opts, operation, merged_operation_opts)

        %{relation | opts: updated_opts}
      end
    end
  end

  def on(:after_compile, relation, _) do
    schema = context(relation, :schema)
    ecto_schema = Generator.generate_module_content(relation.schema(), schema.block || [])

    Module.create(
      relation.__schema_module__(),
      ecto_schema,
      Macro.Env.location(__ENV__)
    )

    quote location: :keep do
      defimpl Ecto.Queryable, for: unquote(relation) do
        @compilers [
          restrict: Operations.Restrict.FieldsCompiler,
          order: Operations.Order.FieldsCompiler,
          preload: Operations.Preload.AssociationCompiler
        ]

        def to_query(%{operations: [], queryable: queryable}) do
          Ecto.Queryable.to_query(queryable)
        end

        def to_query(%{operations: operations, queryable: queryable, opts: opts} = relation) do
          Enum.reduce(operations, Ecto.Queryable.to_query(queryable), fn name, query ->
            case @compilers[name].visit(relation, %{query: query, opts: opts}) do
              {:ok, result_query} ->
                result_query

              {:error, errors} ->
                raise InvalidQueryError, errors: errors
            end
          end)
        end
      end
    end
  end

  def ecto_schema_module(relation) do
    namespace = config(relation, :ecto_schema_namespace)

    module =
      case context(relation, :schema).opts[:struct] do
        nil ->
          config(relation, :ecto_schema_module)

        value ->
          value
      end

    Module.concat(namespace ++ [module])
  end
end
