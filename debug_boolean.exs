Mix.install([{:drops_relation, path: "."}])

# Start the application
Application.ensure_all_started(:drops_relation)

# Configure the test repo
Application.put_env(:drops_relation, :test_repo, Drops.Relation.Repos.Postgres)

# Define a test relation
defmodule TestRelation do
  use Drops.Relation, repo: Drops.Relation.Repos.Postgres, name: "metadata_test"
end

# Get the schema and inspect the is_enabled field
schema = TestRelation.schema()
is_enabled_field = Enum.find(schema.fields, &(&1.name == :is_enabled))

IO.inspect(is_enabled_field, label: "is_enabled field")

# Test the boolean field detection
alias Drops.Relation.Plugins.Queryable.Operations.Compiler
result = Compiler.is_boolean_field?(is_enabled_field)
IO.inspect(result, label: "is_boolean_field? result")
